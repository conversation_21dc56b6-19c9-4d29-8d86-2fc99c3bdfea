/**
 * Color Controller
 * Manages the 3-color gradient system for activation visualization
 */

class ColorController {
    constructor(defaultColors) {
        this.colors = {
            low: defaultColors.low || '#0000ff',
            medium: defaultColors.medium || '#ffff00',
            high: defaultColors.high || '#ff0000'
        };

        this.initializeColorInputs();
    }

    initializeColorInputs() {
        // Set initial values for color inputs
        document.getElementById('color1').value = this.colors.low;
        document.getElementById('color2').value = this.colors.medium;
        document.getElementById('color3').value = this.colors.high;

        // Add event listeners for real-time preview
        document.getElementById('color1').addEventListener('input', (e) => {
            this.colors.low = e.target.value;
        });

        document.getElementById('color2').addEventListener('input', (e) => {
            this.colors.medium = e.target.value;
        });

        document.getElementById('color3').addEventListener('input', (e) => {
            this.colors.high = e.target.value;
        });
    }

    getCurrentColors() {
        return {
            low: this.colors.low,
            medium: this.colors.medium,
            high: this.colors.high
        };
    }

    setColors(colors) {
        this.colors = { ...colors };
        this.updateColorInputs();
    }

    updateColorInputs() {
        document.getElementById('color1').value = this.colors.low;
        document.getElementById('color2').value = this.colors.medium;
        document.getElementById('color3').value = this.colors.high;
    }

    /**
     * Generate a color based on activation intensity (0-1)
     * Uses smooth interpolation between the three colors
     */
    getColorForIntensity(intensity) {
        // Clamp intensity to 0-1 range
        intensity = Math.max(0, Math.min(1, intensity));

        if (intensity <= 0.5) {
            // Interpolate between low and medium
            const t = intensity * 2; // Scale to 0-1
            return this.interpolateColors(this.colors.low, this.colors.medium, t);
        } else {
            // Interpolate between medium and high
            const t = (intensity - 0.5) * 2; // Scale to 0-1
            return this.interpolateColors(this.colors.medium, this.colors.high, t);
        }
    }

    /**
     * Interpolate between two hex colors
     */
    interpolateColors(color1, color2, t) {
        const rgb1 = this.hexToRgb(color1);
        const rgb2 = this.hexToRgb(color2);

        const r = Math.round(rgb1.r + (rgb2.r - rgb1.r) * t);
        const g = Math.round(rgb1.g + (rgb2.g - rgb1.g) * t);
        const b = Math.round(rgb1.b + (rgb2.b - rgb1.b) * t);

        return { r, g, b };
    }

    /**
     * Convert hex color to RGB object
     */
    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : { r: 0, g: 0, b: 0 };
    }

    /**
     * Convert RGB object to hex string
     */
    rgbToHex(rgb) {
        const toHex = (c) => {
            const hex = c.toString(16);
            return hex.length === 1 ? '0' + hex : hex;
        };
        return `#${toHex(rgb.r)}${toHex(rgb.g)}${toHex(rgb.b)}`;
    }

    /**
     * Generate a gradient canvas for preview purposes
     */
    createGradientPreview(width = 300, height = 50) {
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');

        // Create gradient
        const gradient = ctx.createLinearGradient(0, 0, width, 0);
        gradient.addColorStop(0, this.colors.low);
        gradient.addColorStop(0.5, this.colors.medium);
        gradient.addColorStop(1, this.colors.high);

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, width, height);

        return canvas;
    }

    /**
     * Get color palette as an array of RGB values for efficient processing
     */
    getColorPalette(steps = 256) {
        const palette = [];
        for (let i = 0; i < steps; i++) {
            const intensity = i / (steps - 1);
            palette.push(this.getColorForIntensity(intensity));
        }
        return palette;
    }

    /**
     * Reset colors to default values
     */
    resetToDefaults() {
        this.colors = {
            low: '#0000ff',
            medium: '#ffff00',
            high: '#ff0000'
        };
        this.updateColorInputs();
    }

    /**
     * Load color scheme from predefined presets
     */
    loadPreset(presetName) {
        const presets = {
            heatmap: {
                low: '#000080',
                medium: '#ff8000',
                high: '#ff0000'
            },
            ocean: {
                low: '#000080',
                medium: '#0080ff',
                high: '#00ffff'
            },
            forest: {
                low: '#004000',
                medium: '#80ff00',
                high: '#ffff00'
            },
            sunset: {
                low: '#800080',
                medium: '#ff8000',
                high: '#ffff00'
            },
            grayscale: {
                low: '#000000',
                medium: '#808080',
                high: '#ffffff'
            }
        };

        if (presets[presetName]) {
            this.setColors(presets[presetName]);
        }
    }

    /**
     * Export current color configuration
     */
    exportColors() {
        return JSON.stringify(this.colors, null, 2);
    }

    /**
     * Import color configuration from JSON
     */
    importColors(jsonString) {
        try {
            const colors = JSON.parse(jsonString);
            if (colors.low && colors.medium && colors.high) {
                this.setColors(colors);
                return true;
            }
        } catch (error) {
            console.error('Failed to import colors:', error);
        }
        return false;
    }
}