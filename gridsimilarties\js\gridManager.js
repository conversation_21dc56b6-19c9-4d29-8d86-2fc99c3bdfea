/**
 * Grid Manager
 * Handles the creation and management of the 4x6 activation grid
 */

class GridManager {
    constructor(config) {
        this.config = config;
        this.gridContainer = document.getElementById('activationGrid');
        this.cells = new Map();
        this.currentMode = 'picture';
    }

    async createGrid() {
        this.gridContainer.innerHTML = '';

        for (let i = 1; i <= this.config.grid.totalCells; i++) {
            const cell = await this.createGridCell(i);
            this.cells.set(i, cell);
            this.gridContainer.appendChild(cell.element);
        }
    }

    async createGridCell(index) {
        const cellElement = document.createElement('div');
        cellElement.className = 'grid-cell';
        cellElement.setAttribute('data-cell-index', index);

        // Create cell label
        const label = document.createElement('div');
        label.className = 'cell-label';
        label.textContent = `Cell ${index}`;
        cellElement.appendChild(label);

        // Create image element
        const img = document.createElement('img');
        img.className = 'cell-image';
        img.alt = `Base image ${index}`;

        // Try to load base image
        const baseImagePath = await this.findImageFile(this.config.paths.baseImages, `baseimage${index}`);
        if (baseImagePath) {
            img.src = baseImagePath;
        } else {
            // Create placeholder if no image found
            img.src = this.createPlaceholderImage(index);
        }

        cellElement.appendChild(img);

        // Create video element
        const video = document.createElement('video');
        video.className = 'cell-video';
        video.muted = this.config.video.muted;
        video.loop = this.config.video.loop;
        video.autoplay = this.config.video.autoplay;
        video.controls = this.config.video.controls;

        // Try to load video
        const videoPath = await this.findVideoFile(this.config.paths.videos, 'main');
        if (videoPath) {
            video.src = videoPath;
        }

        cellElement.appendChild(video);

        // Create activation overlay canvas
        const overlayCanvas = document.createElement('canvas');
        overlayCanvas.className = 'activation-overlay';
        cellElement.appendChild(overlayCanvas);

        return {
            element: cellElement,
            image: img,
            video: video,
            overlay: overlayCanvas,
            index: index
        };
    }

    async findImageFile(basePath, filename) {
        for (const ext of this.config.fileFormats.images) {
            const path = `${basePath}${filename}.${ext}`;
            if (await this.fileExists(path)) {
                return path;
            }
        }
        return null;
    }

    async findVideoFile(basePath, filename) {
        for (const ext of this.config.fileFormats.videos) {
            const path = `${basePath}${filename}.${ext}`;
            if (await this.fileExists(path)) {
                return path;
            }
        }
        return null;
    }

    async fileExists(path) {
        try {
            const response = await fetch(path, { method: 'HEAD' });
            return response.ok;
        } catch {
            return false;
        }
    }

    createPlaceholderImage(index) {
        const canvas = document.createElement('canvas');
        canvas.width = 400;
        canvas.height = 400;
        const ctx = canvas.getContext('2d');

        // Create gradient background
        const gradient = ctx.createLinearGradient(0, 0, 400, 400);
        gradient.addColorStop(0, '#333333');
        gradient.addColorStop(1, '#555555');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 400, 400);

        // Add text
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 24px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(`Base Image ${index}`, 200, 180);
        ctx.fillText('Not Found', 200, 220);

        return canvas.toDataURL();
    }

    switchMode(mode) {
        this.currentMode = mode;

        this.cells.forEach((cell) => {
            if (mode === 'video') {
                cell.element.classList.add('video-mode');
                if (cell.video.src) {
                    cell.video.play().catch(e => console.warn('Video autoplay failed:', e));
                }
            } else {
                cell.element.classList.remove('video-mode');
                if (cell.video.src) {
                    cell.video.pause();
                }
            }
        });
    }

    setActivationOverlay(cellIndex, overlayCanvas) {
        const cell = this.cells.get(cellIndex);
        if (!cell) return;

        const targetCanvas = cell.overlay;
        const targetCtx = targetCanvas.getContext('2d');

        // Set canvas size to match cell
        const cellRect = cell.element.getBoundingClientRect();
        targetCanvas.width = cellRect.width || 200;
        targetCanvas.height = cellRect.height || 200;

        // Draw the activation overlay
        targetCtx.clearRect(0, 0, targetCanvas.width, targetCanvas.height);
        targetCtx.drawImage(overlayCanvas, 0, 0, targetCanvas.width, targetCanvas.height);
    }

    getCell(index) {
        return this.cells.get(index);
    }

    getAllCells() {
        return Array.from(this.cells.values());
    }

    updateCellImage(index, imageSrc) {
        const cell = this.cells.get(index);
        if (cell) {
            cell.image.src = imageSrc;
        }
    }

    updateCellVideo(index, videoSrc) {
        const cell = this.cells.get(index);
        if (cell) {
            cell.video.src = videoSrc;
            if (this.currentMode === 'video') {
                cell.video.play().catch(e => console.warn('Video play failed:', e));
            }
        }
    }

    clearActivationOverlay(cellIndex) {
        const cell = this.cells.get(cellIndex);
        if (cell) {
            const ctx = cell.overlay.getContext('2d');
            ctx.clearRect(0, 0, cell.overlay.width, cell.overlay.height);
        }
    }

    clearAllActivationOverlays() {
        this.cells.forEach((_, index) => {
            this.clearActivationOverlay(index);
        });
    }
}