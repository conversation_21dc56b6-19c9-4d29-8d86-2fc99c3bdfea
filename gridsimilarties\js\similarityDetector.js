/**
 * Similarity Detector
 * Implements algorithmic similarity detection with spatial tolerance
 */

class SimilarityDetector {
    constructor(config) {
        this.tolerance = config.defaultTolerance || 3;
        this.sensitivity = config.defaultSensitivity || 0.5;
        this.maxTolerance = config.maxTolerance || 10;
        this.minSensitivity = config.minSensitivity || 0.1;
        this.maxSensitivity = config.maxSensitivity || 1.0;
    }

    setTolerance(tolerance) {
        this.tolerance = Math.max(1, Math.min(this.maxTolerance, tolerance));
    }

    setSensitivity(sensitivity) {
        this.sensitivity = Math.max(this.minSensitivity, Math.min(this.maxSensitivity, sensitivity));
    }

    /**
     * Calculate similarity map between base image and activation image
     * Returns a 2D array of similarity values (0-1)
     */
    async calculateSimilarity(baseImage, activationImage) {
        // Create canvases for image processing
        const baseCanvas = this.createCanvas(baseImage);
        const activationCanvas = this.createCanvas(activationImage);

        const baseCtx = baseCanvas.getContext('2d');
        const activationCtx = activationCanvas.getContext('2d');

        // Draw images to canvases
        baseCtx.drawImage(baseImage, 0, 0, baseCanvas.width, baseCanvas.height);
        activationCtx.drawImage(activationImage, 0, 0, activationCanvas.width, activationCanvas.height);

        // Get image data
        const baseData = baseCtx.getImageData(0, 0, baseCanvas.width, baseCanvas.height);
        const activationData = activationCtx.getImageData(0, 0, activationCanvas.width, activationCanvas.height);

        // Calculate similarity map
        return this.computeSimilarityMap(baseData, activationData, baseCanvas.width, baseCanvas.height);
    }

    createCanvas(image, maxSize = 512) {
        const canvas = document.createElement('canvas');

        // Calculate dimensions maintaining aspect ratio
        let { width, height } = this.calculateDimensions(image.width, image.height, maxSize);

        canvas.width = width;
        canvas.height = height;

        return canvas;
    }

    calculateDimensions(originalWidth, originalHeight, maxSize) {
        const aspectRatio = originalWidth / originalHeight;

        let width, height;
        if (originalWidth > originalHeight) {
            width = Math.min(originalWidth, maxSize);
            height = width / aspectRatio;
        } else {
            height = Math.min(originalHeight, maxSize);
            width = height * aspectRatio;
        }

        return {
            width: Math.round(width),
            height: Math.round(height)
        };
    }

    computeSimilarityMap(baseData, activationData, width, height) {
        const similarityMap = [];

        for (let y = 0; y < height; y++) {
            const row = [];
            for (let x = 0; x < width; x++) {
                const similarity = this.calculatePixelSimilarity(
                    baseData, activationData, x, y, width, height
                );
                row.push(similarity);
            }
            similarityMap.push(row);
        }

        return similarityMap;
    }

    calculatePixelSimilarity(baseData, activationData, x, y, width, height) {
        const basePixel = this.getPixelRGB(baseData, x, y, width);
        let maxSimilarity = 0;

        // Check pixels within tolerance radius
        const tolerance = this.tolerance;
        for (let dy = -tolerance; dy <= tolerance; dy++) {
            for (let dx = -tolerance; dx <= tolerance; dx++) {
                const nx = x + dx;
                const ny = y + dy;

                // Check bounds
                if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                    const activationPixel = this.getPixelRGB(activationData, nx, ny, width);
                    const similarity = this.calculateColorSimilarity(basePixel, activationPixel);
                    maxSimilarity = Math.max(maxSimilarity, similarity);
                }
            }
        }

        // Apply sensitivity adjustment
        return this.applySensitivity(maxSimilarity);
    }

    getPixelRGB(imageData, x, y, width) {
        const index = (y * width + x) * 4;
        return {
            r: imageData.data[index],
            g: imageData.data[index + 1],
            b: imageData.data[index + 2],
            a: imageData.data[index + 3]
        };
    }

    calculateColorSimilarity(color1, color2) {
        // Use Euclidean distance in RGB space
        const dr = color1.r - color2.r;
        const dg = color1.g - color2.g;
        const db = color1.b - color2.b;

        const distance = Math.sqrt(dr * dr + dg * dg + db * db);
        const maxDistance = Math.sqrt(255 * 255 * 3); // Maximum possible distance

        // Convert distance to similarity (0-1, where 1 is identical)
        return 1 - (distance / maxDistance);
    }

    applySensitivity(similarity) {
        // Apply sensitivity curve
        // Higher sensitivity makes more pixels activate
        const adjusted = Math.pow(similarity, 1 / this.sensitivity);
        return Math.max(0, Math.min(1, adjusted));
    }

    /**
     * Alternative similarity calculation using HSV color space
     */
    calculateColorSimilarityHSV(color1, color2) {
        const hsv1 = this.rgbToHsv(color1);
        const hsv2 = this.rgbToHsv(color2);

        // Weight different components differently
        const hueWeight = 0.4;
        const satWeight = 0.3;
        const valWeight = 0.3;

        // Handle hue wraparound
        let hueDiff = Math.abs(hsv1.h - hsv2.h);
        hueDiff = Math.min(hueDiff, 360 - hueDiff) / 180; // Normalize to 0-1

        const satDiff = Math.abs(hsv1.s - hsv2.s);
        const valDiff = Math.abs(hsv1.v - hsv2.v);

        const totalDiff = hueWeight * hueDiff + satWeight * satDiff + valWeight * valDiff;
        return 1 - totalDiff;
    }

    rgbToHsv(rgb) {
        const r = rgb.r / 255;
        const g = rgb.g / 255;
        const b = rgb.b / 255;

        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        const diff = max - min;

        let h = 0;
        if (diff !== 0) {
            if (max === r) {
                h = ((g - b) / diff) % 6;
            } else if (max === g) {
                h = (b - r) / diff + 2;
            } else {
                h = (r - g) / diff + 4;
            }
        }
        h = (h * 60 + 360) % 360;

        const s = max === 0 ? 0 : diff / max;
        const v = max;

        return { h, s, v };
    }

    /**
     * Post-process similarity map with smoothing
     */
    smoothSimilarityMap(similarityMap, radius = 1) {
        const height = similarityMap.length;
        const width = similarityMap[0].length;
        const smoothed = [];

        for (let y = 0; y < height; y++) {
            const row = [];
            for (let x = 0; x < width; x++) {
                let sum = 0;
                let count = 0;

                for (let dy = -radius; dy <= radius; dy++) {
                    for (let dx = -radius; dx <= radius; dx++) {
                        const ny = y + dy;
                        const nx = x + dx;

                        if (ny >= 0 && ny < height && nx >= 0 && nx < width) {
                            sum += similarityMap[ny][nx];
                            count++;
                        }
                    }
                }

                row.push(sum / count);
            }
            smoothed.push(row);
        }

        return smoothed;
    }

    /**
     * Apply threshold to similarity map
     */
    applySimilarityThreshold(similarityMap, threshold = 0.3) {
        return similarityMap.map(row =>
            row.map(value => value >= threshold ? value : 0)
        );
    }
}