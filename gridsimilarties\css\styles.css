/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #1a1a1a;
    color: #ffffff;
    overflow-x: auto;
}

.container {
    max-width: 1800px;
    margin: 0 auto;
    padding: 20px;
}

/* Control Panel Styles */
.control-panel {
    background: linear-gradient(135deg, #2d2d2d, #3a3a3a);
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border: 1px solid #444;
}

.control-panel h1 {
    text-align: center;
    margin-bottom: 30px;
    color: #ffffff;
    font-size: 2.2em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.control-panel h3 {
    margin-bottom: 15px;
    color: #e0e0e0;
    font-size: 1.1em;
}

/* Mode Controls */
.mode-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
}

.mode-btn {
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    background: linear-gradient(135deg, #4a4a4a, #5a5a5a);
    color: #ffffff;
    font-size: 1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.mode-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.mode-btn.active {
    background: linear-gradient(135deg, #007acc, #0099ff);
    box-shadow: 0 4px 15px rgba(0, 153, 255, 0.3);
}

/* Color Controls */
.color-controls {
    margin-bottom: 25px;
}

.color-picker-group {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
    justify-content: center;
}

.color-input {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.color-input label {
    font-size: 0.9em;
    color: #cccccc;
    font-weight: 500;
}

.color-input input[type="color"] {
    width: 60px;
    height: 40px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    background: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

#applyColors {
    display: block;
    margin: 0 auto;
    padding: 10px 25px;
    background: linear-gradient(135deg, #28a745, #34ce57);
    color: white;
    border: none;
    border-radius: 20px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

#applyColors:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

/* Similarity Controls */
.similarity-controls {
    display: flex;
    gap: 30px;
    justify-content: center;
    flex-wrap: wrap;
}

.slider-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.slider-group label {
    font-size: 0.9em;
    color: #cccccc;
    font-weight: 500;
}

.slider-group input[type="range"] {
    width: 150px;
    height: 6px;
    border-radius: 3px;
    background: #444;
    outline: none;
    cursor: pointer;
}

.slider-group input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007acc, #0099ff);
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 153, 255, 0.3);
}

.slider-group span {
    font-weight: 600;
    color: #ffffff;
    background: #444;
    padding: 4px 12px;
    border-radius: 12px;
    min-width: 30px;
    text-align: center;
}

/* Grid Container */
.grid-container {
    background: linear-gradient(135deg, #2a2a2a, #363636);
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border: 1px solid #444;
}

.activation-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    grid-template-rows: repeat(4, 1fr);
    gap: 15px;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
}

/* Grid Cell Styles */
.grid-cell {
    position: relative;
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
    background: #1a1a1a;
    border: 2px solid #444;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.grid-cell:hover {
    border-color: #007acc;
    box-shadow: 0 6px 20px rgba(0, 153, 255, 0.2);
    transform: scale(1.02);
}

.grid-cell img,
.grid-cell video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.grid-cell video {
    display: none;
}

.grid-cell.video-mode img {
    display: none;
}

.grid-cell.video-mode video {
    display: block;
}

/* Activation Overlay */
.activation-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    opacity: 0.7;
    mix-blend-mode: multiply;
    transition: opacity 0.3s ease;
}

.grid-cell:hover .activation-overlay {
    opacity: 0.8;
}

/* Cell Labels */
.cell-label {
    position: absolute;
    top: 5px;
    left: 5px;
    background: rgba(0, 0, 0, 0.7);
    color: #ffffff;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.7em;
    font-weight: 600;
    z-index: 10;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #444;
    border-top: 4px solid #007acc;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-overlay p {
    color: #ffffff;
    font-size: 1.1em;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1400px) {
    .activation-grid {
        max-width: 1200px;
    }
}

@media (max-width: 1200px) {
    .activation-grid {
        max-width: 1000px;
        gap: 12px;
    }

    .container {
        padding: 15px;
    }
}

@media (max-width: 900px) {
    .activation-grid {
        grid-template-columns: repeat(4, 1fr);
        grid-template-rows: repeat(6, 1fr);
        max-width: 800px;
    }

    .color-picker-group {
        flex-direction: column;
        align-items: center;
    }

    .similarity-controls {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 600px) {
    .activation-grid {
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(8, 1fr);
        gap: 8px;
    }

    .mode-controls {
        flex-direction: column;
        align-items: center;
    }

    .control-panel h1 {
        font-size: 1.8em;
    }
}