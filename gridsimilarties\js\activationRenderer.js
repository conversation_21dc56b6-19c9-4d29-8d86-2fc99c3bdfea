/**
 * Activation Renderer
 * Renders similarity maps as colored activation overlays
 */

class ActivationRenderer {
    constructor() {
        this.defaultSize = 512;
    }
    
    /**
     * Create activation overlay canvas from similarity map
     */
    createActivationOverlay(similarityMap, colors, width = null, height = null) {
        const mapHeight = similarityMap.length;
        const mapWidth = similarityMap[0].length;
        
        // Use provided dimensions or default to map dimensions
        const canvasWidth = width || mapWidth;
        const canvasHeight = height || mapHeight;
        
        const canvas = document.createElement('canvas');
        canvas.width = canvasWidth;
        canvas.height = canvasHeight;
        const ctx = canvas.getContext('2d');
        
        // Create image data
        const imageData = ctx.createImageData(canvasWidth, canvasHeight);
        
        // Fill image data based on similarity map
        for (let y = 0; y < canvasHeight; y++) {
            for (let x = 0; x < canvasWidth; x++) {
                // Map canvas coordinates to similarity map coordinates
                const mapX = Math.floor((x / canvasWidth) * mapWidth);
                const mapY = Math.floor((y / canvasHeight) * mapHeight);
                
                const similarity = similarityMap[mapY][mapX];
                const color = this.getColorForSimilarity(similarity, colors);
                
                const index = (y * canvasWidth + x) * 4;
                imageData.data[index] = color.r;     // Red
                imageData.data[index + 1] = color.g; // Green
                imageData.data[index + 2] = color.b; // Blue
                imageData.data[index + 3] = this.getAlphaForSimilarity(similarity); // Alpha
            }
        }
        
        ctx.putImageData(imageData, 0, 0);
        return canvas;
    }
    
    /**
     * Get color for similarity value using the color controller's logic
     */
    getColorForSimilarity(similarity, colors) {
        // Clamp similarity to 0-1 range
        similarity = Math.max(0, Math.min(1, similarity));
        
        if (similarity <= 0.5) {
            // Interpolate between low and medium
            const t = similarity * 2; // Scale to 0-1
            return this.interpolateColors(colors.low, colors.medium, t);
        } else {
            // Interpolate between medium and high
            const t = (similarity - 0.5) * 2; // Scale to 0-1
            return this.interpolateColors(colors.medium, colors.high, t);
        }
    }
    
    /**
     * Get alpha (transparency) value based on similarity
     */
    getAlphaForSimilarity(similarity) {
        // More similar pixels are more opaque
        // Apply a curve to make the effect more dramatic
        const alpha = Math.pow(similarity, 0.5) * 255;
        return Math.round(Math.max(0, Math.min(255, alpha)));
    }
    
    /**
     * Interpolate between two hex colors
     */
    interpolateColors(color1, color2, t) {
        const rgb1 = this.hexToRgb(color1);
        const rgb2 = this.hexToRgb(color2);
        
        const r = Math.round(rgb1.r + (rgb2.r - rgb1.r) * t);
        const g = Math.round(rgb1.g + (rgb2.g - rgb1.g) * t);
        const b = Math.round(rgb1.b + (rgb2.b - rgb1.b) * t);
        
        return { r, g, b };
    }
    
    /**
     * Convert hex color to RGB object
     */
    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : { r: 0, g: 0, b: 0 };
    }
    
    /**
     * Create a heatmap-style overlay with smooth gradients
     */
    createSmoothActivationOverlay(similarityMap, colors, width = null, height = null) {
        const mapHeight = similarityMap.length;
        const mapWidth = similarityMap[0].length;
        
        const canvasWidth = width || mapWidth;
        const canvasHeight = height || mapHeight;
        
        const canvas = document.createElement('canvas');
        canvas.width = canvasWidth;
        canvas.height = canvasHeight;
        const ctx = canvas.getContext('2d');
        
        // Create multiple gradient layers for smooth blending
        this.renderGradientLayers(ctx, similarityMap, colors, canvasWidth, canvasHeight);
        
        return canvas;
    }
    
    /**
     * Render gradient layers for smooth activation visualization
     */
    renderGradientLayers(ctx, similarityMap, colors, canvasWidth, canvasHeight) {
        const mapHeight = similarityMap.length;
        const mapWidth = similarityMap[0].length;
        
        // Create temporary canvas for each layer
        const tempCanvas = document.createElement('canvas');
        tempCanvas.width = canvasWidth;
        tempCanvas.height = canvasHeight;
        const tempCtx = tempCanvas.getContext('2d');
        
        // Render activation points as radial gradients
        for (let y = 0; y < mapHeight; y++) {
            for (let x = 0; x < mapWidth; x++) {
                const similarity = similarityMap[y][x];
                
                if (similarity > 0.1) { // Only render significant activations
                    const canvasX = (x / mapWidth) * canvasWidth;
                    const canvasY = (y / mapHeight) * canvasHeight;
                    
                    this.renderActivationPoint(tempCtx, canvasX, canvasY, similarity, colors);
                }
            }
        }
        
        // Composite the temp canvas onto the main canvas
        ctx.globalCompositeOperation = 'source-over';
        ctx.drawImage(tempCanvas, 0, 0);
    }
    
    /**
     * Render a single activation point as a radial gradient
     */
    renderActivationPoint(ctx, x, y, intensity, colors) {
        const radius = Math.max(5, intensity * 20);
        const color = this.getColorForSimilarity(intensity, colors);
        
        const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);
        gradient.addColorStop(0, `rgba(${color.r}, ${color.g}, ${color.b}, ${intensity * 0.8})`);
        gradient.addColorStop(1, `rgba(${color.r}, ${color.g}, ${color.b}, 0)`);
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(x, y, radius, 0, Math.PI * 2);
        ctx.fill();
    }
    
    /**
     * Create a contour-style activation overlay
     */
    createContourActivationOverlay(similarityMap, colors, levels = 5) {
        const mapHeight = similarityMap.length;
        const mapWidth = similarityMap[0].length;
        
        const canvas = document.createElement('canvas');
        canvas.width = mapWidth;
        canvas.height = mapHeight;
        const ctx = canvas.getContext('2d');
        
        // Create contour levels
        for (let level = 0; level < levels; level++) {
            const threshold = (level + 1) / levels;
            const color = this.getColorForSimilarity(threshold, colors);
            
            this.renderContourLevel(ctx, similarityMap, threshold, color, mapWidth, mapHeight);
        }
        
        return canvas;
    }
    
    /**
     * Render a single contour level
     */
    renderContourLevel(ctx, similarityMap, threshold, color, width, height) {
        ctx.strokeStyle = `rgba(${color.r}, ${color.g}, ${color.b}, 0.6)`;
        ctx.lineWidth = 2;
        
        // Simple contour detection - could be enhanced with marching squares
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                const current = similarityMap[y][x];
                const neighbors = [
                    similarityMap[y-1][x], // top
                    similarityMap[y+1][x], // bottom
                    similarityMap[y][x-1], // left
                    similarityMap[y][x+1]  // right
                ];
                
                // Check if this pixel is on the contour
                const isOnContour = neighbors.some(neighbor => 
                    (current >= threshold && neighbor < threshold) ||
                    (current < threshold && neighbor >= threshold)
                );
                
                if (isOnContour) {
                    ctx.fillStyle = `rgba(${color.r}, ${color.g}, ${color.b}, 0.3)`;
                    ctx.fillRect(x, y, 1, 1);
                }
            }
        }
    }
    
    /**
     * Apply post-processing effects to the activation overlay
     */
    applyPostProcessing(canvas, effects = {}) {
        const ctx = canvas.getContext('2d');
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        
        if (effects.blur) {
            this.applyGaussianBlur(imageData, effects.blur);
        }
        
        if (effects.contrast) {
            this.applyContrast(imageData, effects.contrast);
        }
        
        if (effects.brightness) {
            this.applyBrightness(imageData, effects.brightness);
        }
        
        ctx.putImageData(imageData, 0, 0);
        return canvas;
    }
    
    /**
     * Apply simple contrast adjustment
     */
    applyContrast(imageData, contrast) {
        const factor = (259 * (contrast + 255)) / (255 * (259 - contrast));
        
        for (let i = 0; i < imageData.data.length; i += 4) {
            imageData.data[i] = Math.max(0, Math.min(255, factor * (imageData.data[i] - 128) + 128));
            imageData.data[i + 1] = Math.max(0, Math.min(255, factor * (imageData.data[i + 1] - 128) + 128));
            imageData.data[i + 2] = Math.max(0, Math.min(255, factor * (imageData.data[i + 2] - 128) + 128));
        }
    }
    
    /**
     * Apply brightness adjustment
     */
    applyBrightness(imageData, brightness) {
        for (let i = 0; i < imageData.data.length; i += 4) {
            imageData.data[i] = Math.max(0, Math.min(255, imageData.data[i] + brightness));
            imageData.data[i + 1] = Math.max(0, Math.min(255, imageData.data[i + 1] + brightness));
            imageData.data[i + 2] = Math.max(0, Math.min(255, imageData.data[i + 2] + brightness));
        }
    }
}
